{"schemaVersion": 1, "id": "${mod_id}", "version": "${version}", "name": "${mod_name}", "description": "${description}", "authors": ["${mod_author}"], "contact": {"homepage": "https://modrinth.com/mod/shuffle", "sources": "https://github.com/Trikzon/shuffle", "issues": "https://github.com/Trikzon/shuffle/issues"}, "license": "${license}", "icon": "${mod_id}.png", "environment": "client", "entrypoints": {"client": ["com.diontryban.shuffle.client.ShuffleClientFabric"]}, "mixins": ["${mod_id}.mixins.json", "${mod_id}.fabric.mixins.json"], "depends": {"minecraft": "^${minecraft_version}", "ash_api": "^${ash_version}"}, "recommends": {"modmenu": "^${modmenu_version}"}}