/*
 * This file is part of Shuffle.
 * A copy of this program can be found at https://github.com/Trikzon/shuffle.
 * Copyright (C) 2023 Dion Tryban
 *
 * Shuffle is free software: you can redistribute it and/or modify it under the
 * terms of the GNU Lesser General Public License as published by the Free
 * Software Foundation, either version 3 of the License, or (at your option)
 * any later version.
 *
 * Shuffle is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with Shuffle. If not, see <https://www.gnu.org/licenses/>.
 */

package com.diontryban.shuffle.options;

import com.diontryban.ash_api.options.ModOptions;
import com.google.gson.annotations.SerializedName;

public class ShuffleOptions extends ModOptions {
    @SerializedName("use_weighted_random")
    public boolean useWeightedRandom = false;
    @SerializedName("play_sound_effects")
    public boolean playSoundEffects = true;
    @SerializedName("locked_slots")  // True means slot is locked.
    public boolean[] lockedSlots = { false, false, false, false, false, false, false, false, false };

    @Override
    protected int getVersion() {
        return 2;
    }
}
