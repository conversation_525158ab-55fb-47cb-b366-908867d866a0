name: Publish
on:
  push:
    tags:
      - "*"
env:
  DIONTRYBAN_MAVEN_USER: github
  DIONTRYBAN_MAVEN_TOKEN: ${{ secrets.DIONTRYBAN_MAVEN_TOKEN }}
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: checkout repository
        uses: actions/checkout@v2
      - name: setup jdk 21
        uses: actions/setup-java@v1
        with:
          java-version: "21"
      - name: make gradle wrapper executable
        if: ${{ runner.os != 'Windows' }}
        run: chmod +x ./gradlew
      - name: build
        run: ./gradlew build
      - uses: BrycensRanch/read-properties-action@v1
        id: gradle_props
        with:
          file: gradle.properties
          all: true
      - name: Publish NeoForge to CurseForge and Modrinth
        uses: Kir-Antipov/mc-publish@v3.3  # https://github.com/Kir-Antipov/mc-publish
        with:
          curseforge-id: 360916
          curseforge-token: ${{ secrets.CURSEFORGE_TOKEN }}
          modrinth-id: WUvGZLgB
          modrinth-token: ${{ secrets.MODRINTH_TOKEN }}
          modrinth-featured: false
          files: |
            neoforge/build/libs/!(*-@(dev|sources|javadoc)).jar
          loaders: |
            neoforge
          game-versions: |
            1.21.5
          java: 21
          name: "[NeoForge ${{ steps.gradle_props.outputs.minecraft_version }}] v${{ steps.gradle_props.outputs.version }}"
          version: "${{ steps.gradle_props.outputs.version }}-neoforge"
          changelog-file: "MINI_CHANGELOG.md"
          dependencies: "ash-api"
      - name: Publish Fabric to CurseForge and Modrinth
        uses: Kir-Antipov/mc-publish@v3.3  # https://github.com/Kir-Antipov/mc-publish
        with:
          curseforge-id: 360916
          curseforge-token: ${{ secrets.CURSEFORGE_TOKEN }}
          modrinth-id: WUvGZLgB
          modrinth-token: ${{ secrets.MODRINTH_TOKEN }}
          modrinth-featured: false
          files: |
            fabric/build/libs/!(*-@(dev|sources|javadoc)).jar
          loaders: |
            fabric
            quilt
          game-versions: |
            1.21.5
          java: 21
          name: "[Fabric ${{ steps.gradle_props.outputs.minecraft_version }}] v${{ steps.gradle_props.outputs.version }}"
          version: "${{ steps.gradle_props.outputs.version }}-fabric"
          changelog-file: "MINI_CHANGELOG.md"
          dependencies: "ash-api"
      - name: Publish All to Maven
        run: ./gradlew publish
      - name: Upload release assets
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            neoforge/build/libs/!(*-@(dev|sources|javadoc)).jar
            fabric/build/libs/!(*-@(dev|sources|javadoc)).jar
          body_path: MINI_CHANGELOG.md
