# Changelog
## v21.5.0
- Update to MC 1.21.5.

## v21.4.0
- Update to MC 1.21.4.

## v21.3.0
- Update to MC 1.21.3.

## v21.2.0
- Update to MC 1.21.2.

## v21.1.0
- Update to MC 1.21.1.

## v21.0.4
- Add hotbar slot locking [#15](https://github.com/Trikzon/shuffle/issues/15), [#16](https://github.com/Trikzon/shuffle/pull/16). Thank you, @ACCBDD, and @ThatChair.

## v21.0.3
- Fix race conditon on NeoForge when registering KeyMappings.

## v21.0.2
- Update build scripts using [MDK Generator](https://github.com/Trikzon/mdk-generator). No functional changes.

## v21.0.1
- Fix options screen rendering duplicate option buttons (Ash API fix)

## v21.0.0
- Update to MC 1.21.

## v20.6.0
- Update to MC 1.20.6.
- Add setting for sound effects.
  - Suggested by @nco2k.

## v20.4.1
- Fixed logo file on NeoForge.

## v20.4.0
- Updated to MC 1.20.4.

## v20.2.0
- Update to MC 1.20.2.
- Changed version format to follow NeoForge's <minecraft_minor>.<minecraft_patch>.<number>(-beta).
    - Read more about it here.
- No longer directly provide Quilt mod loader support. The Fabric version will likely continue to work on Quilt.
- Add support for the NeoForge mod loader.
- Add Norwegian translations. Thank you @Erb3 :D
- Add option for weighted logic when choosing blocks based on stack size. Thank you @ChampionAsh5357 :D
- Fix the mod not being labeled as a client mod my Mod Menu

## v9.0.0+1.20.1
- Update to MC 1.20.1

## v8.1.0+1.20
- Update build scripts
- Require `ash_api` instead of `ash`
- Add minecraft version to the end of version number

## v8.0.0
- Update to MC 1.20

## v7.0.0
- Update to MC 1.19.3 and 1.19.4
- Add Ash API dependency
- Add colors to shuffle mode toggle messages
